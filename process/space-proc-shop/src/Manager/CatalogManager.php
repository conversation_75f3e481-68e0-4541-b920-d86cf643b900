<?php

namespace App\Manager;

use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Webmozart\Assert\Assert;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\BrandHelper;
use App\Model\CatalogModel;
use App\Service\CatalogService;
use App\Service\ContribService;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Manager\UserManager;

/**
 * Catalog Manager.
 */
class CatalogManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    const SOURCE_APP = 'APP';

    public function __construct(private CatalogService $service, private SerializerInterface $serializer, private ContribService $contribService, private ValidatorInterface $validator, private UserManager $userManager)
    {
    }


    public function getCategory(?string $familyName): string
    {
        $connectedServices = ['NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'];
        $afterSalesServices = ['DIMBO', 'PRIVILEGE'];
        if (in_array($familyName, $connectedServices)) {
            return 'CONNECTED_SERVICES';
        } elseif (in_array($familyName, $afterSalesServices)) {
            return 'AFTERSALES_SERVICES';
        } else {
            return 'OTHERS';
        }
    }

    public function getFullCatalogData(array $catalogData, array $contribData): CatalogModel
    {
        if (isset($catalogData['groupName'])) {
            $catalogData['productGroupName'] = $catalogData['groupName'];
            unset($catalogData['groupName']);
        }
        $catalogModel = $this->serializer->denormalize($catalogData, CatalogModel::class);
        $catalogModel->setCategory($this->getCategory($catalogModel->getFamilyName()));
        $catalogModel->setTitle($contribData['title'] ?? "");
        $catalogModel->setUrlSso($contribData['productUrlSso'] ?? "");
        $catalogModel->setTopMainImage($contribData['topMainImage'] ?? "");
        $catalogModel->setDescription($contribData['shortDescription'] ?? "");
        return $catalogModel;
    }

    /**
     * get Catalog data.
     */
    public function getCatalog(array $params): ResponseArrayFormat
    {
        $this->logger->info('=> Call Catalog API : '.__METHOD__.' with parameters : ', $params);
        try {
            $user = $this->userManager->getUserByUserId($params['userId']);
            if ($user->getCode() !== Response::HTTP_OK) {
                return new ErrorResponse($user->getData(), $user->getCode());
            }
            $userData = (array) json_decode($user->getData(), true)['documents'] ?? [];

            if (empty($userData)) {
                $this->logger->error('No user found', ['userId' => $params['userId']]);
                throw new UnrecoverableMessageHandlingException('No user found');
            }
            
            $catalogResponse = $this->service->getCatalog($params);
            if (Response::HTTP_OK == $catalogResponse->getCode()) {
                Assert::isArray($catalogResponse->getData());
                Assert::keyExists($catalogResponse->getData(), 'success');
                $catalogResponseData = $catalogResponse->getData()['success'];
                $catalogDataList = [];
                foreach ($catalogResponseData as $catalogData) {
                    $productId = $catalogData['id'] ?? '';
                    $brand = $params['brand'] ?? '';
                    $cultureCode = $params['language'] . '-' . $params['country'];
                    $contribResponse = $this->contribService->getContrib($brand, $cultureCode, $productId, self::SOURCE_APP);
                    if ($contribResponse->getCode() !== Response::HTTP_OK) {
                        $statusCode = $contribResponse->getCode();
                        $contribResponse = $contribResponse->getData();
                        $result = (isset($contribResponse['error']['message'])) ? $contribResponse['error']['message'] : $contribResponse;
                        $this->logger->error('=> '.__METHOD__.' => error : '.$result);
                        return new ErrorResponse($result, $statusCode);
                    }
                    $contribData = $contribResponse->getData()['success'];
                    $catalogData = $this->getFullCatalogData($catalogData, $contribData);
                    $violations = $this->validator->validate($catalogData, null, ['catalogResponse']);
                    if (0 < count($violations)) {
                        $violationsDescription = $this->getValidationMessages($violations);
                        $missingKeys = array_keys($violationsDescription);
                        $keysString = implode(', ', $missingKeys);
                        $message = "The following fields are missing data from the SAMS API response: $keysString.";
                        $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                            'model' => $catalogData,
                            'desc' => $violationsDescription,
                        ]);
                        return new ErrorResponse($message, 500);
                    }
                    $catalogData = json_decode($this->serializer->serialize($catalogData, 'json'), true);
                    $catalogDataList[] = $catalogData;
                }

                return new SuccessResponse($catalogDataList);
            }
            $responseData = $catalogResponse->getData();
            Assert::isArray($responseData);
            Assert::keyExists($responseData, 'error');
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;

            $this->logger->error('=> '.__METHOD__.' => error : '.$result);

            return new ErrorResponse($result, $catalogResponse->getCode());
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.'Catched Exception CatalogManager::getCatalog '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
