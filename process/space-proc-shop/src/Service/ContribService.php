<?php

namespace App\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;

class ContribService
{
    use LoggerTrait;

    public function __construct(
        private SysSamsDataConnector $connector
    ) {
    }

    public function getContrib(string $brand, string $culture, string $productId, ?string $source = 'APP'): WSResponse
    {
        $options = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
            ]
        ];
        $url = '/v1/sams/contrib/getInfoByProduct/' . $productId;
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);

        return $response;
    }
}