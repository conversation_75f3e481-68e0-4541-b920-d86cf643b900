<?php

namespace App\Command;

use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Psr\Log\LoggerInterface;
use Doctrine\ODM\MongoDB\DocumentManager;
use MongoDB\BSON\ObjectId;

#[AsCommand(
    name: 'app:delete-duplicate-users',
    description: 'Delete duplicate UserData documents in MongoDB based on analysis'
)]
class DeleteDuplicateUsersCommand extends Command
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private DocumentManager $documentManager,
        private LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('user-id', 'u', InputOption::VALUE_OPTIONAL, 'Delete duplicates for specific user ID only')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Preview deletions without actually deleting')
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Skip confirmation prompts (use with caution)')
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limit number of users to process', 10)
            ->addOption('export-log', 'x', InputOption::VALUE_OPTIONAL, 'Export deletion log to JSON file', false)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $isDryRun = $input->getOption('dry-run');
        $isForced = $input->getOption('force');
        
        if ($isDryRun) {
            $io->title('MongoDB UserData Duplicate Deleter - DRY RUN MODE');
            $io->note('No actual deletions will be performed');
        } else {
            $io->title('MongoDB UserData Duplicate Deleter - LIVE MODE');
            $io->warning('This will permanently delete documents!');
        }
        
        try {
            // Find duplicates using the same logic as the finder
            $duplicates = $this->findDuplicates($input->getOption('user-id'));
            
            if (empty($duplicates)) {
                $io->success('No duplicate UserData documents found!');
                return Command::SUCCESS;
            }
            
            $totalUsers = count($duplicates);
            $totalDuplicateDocs = array_sum(array_map(fn($item) => $item['count'] - 1, $duplicates));
            
            $io->warning("Found {$totalUsers} users with duplicate documents");
            $io->note("Total duplicate documents to delete: {$totalDuplicateDocs}");
            
            // Limit processing if specified
            $limit = (int)$input->getOption('limit');
            if ($limit > 0 && $totalUsers > $limit) {
                $io->note("Processing limited to first {$limit} users");
                $duplicates = array_slice($duplicates, 0, $limit, true);
            }
            
            // Show preview of what will be deleted
            $this->showDeletionPreview($io, $duplicates);
            
            // Confirm deletion if not forced and not dry run
            if (!$isDryRun && !$isForced) {
                $helper = $this->getHelper('question');
                $question = new ConfirmationQuestion(
                    'Are you sure you want to delete these duplicate documents? (y/N) ',
                    false
                );
                
                if (!$helper->ask($input, $output, $question)) {
                    $io->note('Deletion cancelled by user');
                    return Command::SUCCESS;
                }
            }
            
            // Execute deletion
            $results = $this->executeDeletion($duplicates, $isDryRun, $io);
            
            // Show results
            $this->showResults($io, $results, $isDryRun);
            
            // Export log if requested
            if ($input->getOption('export-log') !== false) {
                $filename = $input->getOption('export-log') ?: 'deletion_log_' . date('Y-m-d_H-i-s') . '.json';
                $this->exportLog($results, $filename, $io);
            }
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $io->error('Error during deletion: ' . $e->getMessage());
            $this->logger->error('DeleteDuplicateUsersCommand error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    private function findDuplicates(?string $specificUserId = null): array
    {
        $collection = $this->documentManager->getDocumentCollection(UserData::class);
        
        $pipeline = [
            [
                '$group' => [
                    '_id' => '$userId',
                    'count' => ['$sum' => 1],
                    'documents' => [
                        '$push' => [
                            'doc_id' => '$_id',
                            'userId' => '$userId',
                            'vehicle_count' => ['$size' => ['$ifNull' => ['$vehicle', []]]],
                            'userPsaId_count' => ['$size' => ['$ifNull' => ['$userPsaId', []]]],
                            'has_preferredDealer' => [
                                '$cond' => [
                                    'if' => ['$ne' => ['$preferredDealer', null]],
                                    'then' => true,
                                    'else' => false
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$match' => [
                    'count' => ['$gt' => 1]
                ]
            ],
            [
                '$sort' => [
                    'count' => -1
                ]
            ]
        ];
        
        // Add filter for specific user if provided
        if ($specificUserId) {
            array_unshift($pipeline, [
                '$match' => [
                    'userId' => $specificUserId
                ]
            ]);
        }
        
        $result = $collection->aggregate($pipeline)->toArray();
        
        // Convert to associative array
        $duplicates = [];
        foreach ($result as $item) {
            $duplicates[$item['_id']] = [
                'count' => $item['count'],
                'documents' => $item['documents']
            ];
        }
        
        return $duplicates;
    }

    private function scoreDocuments(array $documents): array
    {
        $scoredDocs = [];
        
        foreach ($documents as $doc) {
            $score = 0;
            
            // Score based on vehicle count (10 points per vehicle)
            $score += $doc['vehicle_count'] * 10;
            
            // Score based on userPsaId count (5 points per PSA ID)
            $score += $doc['userPsaId_count'] * 5;
            
            // Score based on preferredDealer presence (20 points)
            if ($doc['has_preferredDealer']) {
                $score += 20;
            }
            
            // Score based on document recency (newer ObjectIds get higher score)
            try {
                $objectId = new ObjectId($doc['doc_id']);
                $timestamp = $objectId->getTimestamp();
                $score += $timestamp / 1000000; // Small weight for recency
            } catch (\Exception $e) {
                // Ignore timestamp scoring if ObjectId parsing fails
            }
            
            $scoredDocs[] = [
                'document' => $doc,
                'score' => $score
            ];
        }
        
        // Sort by score (highest first)
        usort($scoredDocs, fn($a, $b) => $b['score'] <=> $a['score']);
        
        return $scoredDocs;
    }

    private function showDeletionPreview(SymfonyStyle $io, array $duplicates): void
    {
        $io->section('Deletion Preview');
        
        $table = new Table($io);
        $table->setHeaders(['User ID', 'Total Docs', 'Keep Document', 'Delete Count', 'Keep Reason']);
        
        foreach ($duplicates as $userId => $data) {
            $scoredDocs = $this->scoreDocuments($data['documents']);
            $keepDoc = $scoredDocs[0]['document'];
            $deleteCount = count($scoredDocs) - 1;
            
            $keepReason = [];
            if ($keepDoc['vehicle_count'] > 0) $keepReason[] = "{$keepDoc['vehicle_count']} vehicles";
            if ($keepDoc['userPsaId_count'] > 0) $keepReason[] = "{$keepDoc['userPsaId_count']} PSA IDs";
            if ($keepDoc['has_preferredDealer']) $keepReason[] = "has dealer";
            if (empty($keepReason)) $keepReason[] = "newest";
            
            $table->addRow([
                strlen($userId) > 20 ? substr($userId, 0, 20) . '...' : $userId,
                $data['count'],
                substr($keepDoc['doc_id'], -8),
                $deleteCount,
                implode(', ', $keepReason)
            ]);
        }
        
        $table->render();
    }

    private function executeDeletion(array $duplicates, bool $isDryRun, SymfonyStyle $io): array
    {
        $results = [
            'users_processed' => 0,
            'documents_deleted' => 0,
            'errors' => [],
            'details' => []
        ];
        
        $collection = $this->documentManager->getDocumentCollection(UserData::class);
        
        foreach ($duplicates as $userId => $data) {
            try {
                $scoredDocs = $this->scoreDocuments($data['documents']);
                $keepDoc = $scoredDocs[0]['document'];
                $docsToDelete = array_slice($scoredDocs, 1);
                
                $deletedCount = 0;
                $deletedIds = [];
                
                foreach ($docsToDelete as $docData) {
                    $docId = $docData['document']['doc_id'];
                    
                    if ($isDryRun) {
                        $io->writeln("  [DRY RUN] Would delete: {$docId}");
                        $deletedCount++;
                        $deletedIds[] = $docId;
                    } else {
                        try {
                            $deleteResult = $collection->deleteOne(['_id' => new ObjectId($docId)]);
                            
                            if ($deleteResult->getDeletedCount() === 1) {
                                $io->writeln("  ✅ Deleted: {$docId}");
                                $deletedCount++;
                                $deletedIds[] = $docId;
                            } else {
                                $error = "Failed to delete document {$docId} for user {$userId}";
                                $io->writeln("  ❌ {$error}");
                                $results['errors'][] = $error;
                            }
                        } catch (\Exception $e) {
                            $error = "Error deleting document {$docId}: " . $e->getMessage();
                            $io->writeln("  ❌ {$error}");
                            $results['errors'][] = $error;
                        }
                    }
                }
                
                $results['users_processed']++;
                $results['documents_deleted'] += $deletedCount;
                $results['details'][] = [
                    'user_id' => $userId,
                    'kept_document' => $keepDoc['doc_id'],
                    'deleted_documents' => $deletedIds,
                    'deleted_count' => $deletedCount
                ];
                
                $io->writeln("✅ Processed user {$userId}: kept 1, deleted {$deletedCount}");
                
            } catch (\Exception $e) {
                $error = "Error processing user {$userId}: " . $e->getMessage();
                $io->writeln("❌ {$error}");
                $results['errors'][] = $error;
            }
        }
        
        return $results;
    }

    private function showResults(SymfonyStyle $io, array $results, bool $isDryRun): void
    {
        $io->section($isDryRun ? 'Dry Run Results' : 'Deletion Results');
        
        $io->definitionList(
            ['Users processed' => $results['users_processed']],
            ['Documents deleted' => $results['documents_deleted']],
            ['Errors encountered' => count($results['errors'])]
        );
        
        if (!empty($results['errors'])) {
            $io->error('Errors encountered:');
            foreach ($results['errors'] as $error) {
                $io->writeln("  • {$error}");
            }
        }
        
        if ($isDryRun) {
            $io->success('Dry run completed successfully. Use without --dry-run to perform actual deletions.');
        } else {
            $io->success('Deletion completed successfully!');
        }
    }

    private function exportLog(array $results, string $filename, SymfonyStyle $io): void
    {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'summary' => [
                    'users_processed' => $results['users_processed'],
                    'documents_deleted' => $results['documents_deleted'],
                    'errors_count' => count($results['errors'])
                ],
                'details' => $results['details'],
                'errors' => $results['errors']
            ];
            
            file_put_contents($filename, json_encode($logData, JSON_PRETTY_PRINT));
            $io->success("Deletion log exported to: {$filename}");
            
        } catch (\Exception $e) {
            $io->error("Failed to export log: " . $e->getMessage());
        }
    }
}
