<?php

namespace App\Command;

use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Helper\Table;
use Psr\Log\LoggerInterface;
use Doctrine\ODM\MongoDB\DocumentManager;

#[AsCommand(
    name: 'app:find-duplicate-users',
    description: 'Find and analyze duplicate UserData documents in MongoDB'
)]
class FindDuplicateUsersCommand extends Command
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private DocumentManager $documentManager,
        private LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('user-id', 'u', InputOption::VALUE_OPTIONAL, 'Analyze specific user ID only')
            ->addOption('export', 'x', InputOption::VALUE_OPTIONAL, 'Export results to JSON file', false)
            ->addOption('detailed', 'd', InputOption::VALUE_NONE, 'Show detailed document information')
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limit number of results to display', 20)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $io->title('MongoDB UserData Duplicate Finder');
        
        try {
            // Find duplicates using MongoDB aggregation
            $duplicates = $this->findDuplicates($input->getOption('user-id'));
            
            if (empty($duplicates)) {
                $io->success('No duplicate UserData documents found!');
                return Command::SUCCESS;
            }
            
            $totalUsers = count($duplicates);
            $totalDuplicateDocs = array_sum(array_map(fn($item) => $item['count'] - 1, $duplicates));
            
            $io->warning("Found {$totalUsers} users with duplicate documents");
            $io->note("Total duplicate documents: {$totalDuplicateDocs}");
            
            // Display summary table
            $this->displaySummaryTable($io, $duplicates, (int)$input->getOption('limit'));
            
            // Show detailed analysis if requested
            if ($input->getOption('detailed')) {
                $this->displayDetailedAnalysis($io, $duplicates, (int)$input->getOption('limit'));
            }
            
            // Export to JSON if requested
            if ($input->getOption('export') !== false) {
                $filename = $input->getOption('export') ?: 'duplicate_users_' . date('Y-m-d_H-i-s') . '.json';
                $this->exportToJson($duplicates, $filename, $io);
            }
            
            // Show recommendations
            $this->showRecommendations($io, $duplicates);
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $io->error('Error finding duplicates: ' . $e->getMessage());
            $this->logger->error('FindDuplicateUsersCommand error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    private function findDuplicates(?string $specificUserId = null): array
    {
        $collection = $this->documentManager->getDocumentCollection(UserData::class);
        
        $pipeline = [
            [
                '$group' => [
                    '_id' => '$userId',
                    'count' => ['$sum' => 1],
                    'documents' => [
                        '$push' => [
                            'doc_id' => '$_id',
                            'userId' => '$userId',
                            'vehicle_count' => ['$size' => ['$ifNull' => ['$vehicle', []]]],
                            'userPsaId_count' => ['$size' => ['$ifNull' => ['$userPsaId', []]]],
                            'has_preferredDealer' => [
                                '$cond' => [
                                    'if' => ['$ne' => ['$preferredDealer', null]],
                                    'then' => true,
                                    'else' => false
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                '$match' => [
                    'count' => ['$gt' => 1]
                ]
            ],
            [
                '$sort' => [
                    'count' => -1
                ]
            ]
        ];
        
        // Add filter for specific user if provided
        if ($specificUserId) {
            array_unshift($pipeline, [
                '$match' => [
                    'userId' => $specificUserId
                ]
            ]);
        }
        
        $result = $collection->aggregate($pipeline)->toArray();
        
        // Convert to associative array
        $duplicates = [];
        foreach ($result as $item) {
            $duplicates[$item['_id']] = [
                'count' => $item['count'],
                'documents' => $item['documents']
            ];
        }
        
        return $duplicates;
    }

    private function displaySummaryTable(SymfonyStyle $io, array $duplicates, int $limit): void
    {
        $io->section('Duplicate Users Summary');
        
        $table = new Table($io);
        $table->setHeaders(['User ID', 'Duplicates', 'Vehicles Range', 'PSA IDs Range', 'Has Dealer Count']);
        
        $count = 0;
        foreach ($duplicates as $userId => $data) {
            if ($count >= $limit) {
                break;
            }
            
            $documents = $data['documents'];
            $vehicleCounts = array_column($documents, 'vehicle_count');
            $psaIdCounts = array_column($documents, 'userPsaId_count');
            $dealerFlags = array_column($documents, 'has_preferredDealer');
            
            $table->addRow([
                strlen($userId) > 25 ? substr($userId, 0, 25) . '...' : $userId,
                $data['count'],
                min($vehicleCounts) . '-' . max($vehicleCounts),
                min($psaIdCounts) . '-' . max($psaIdCounts),
                array_sum($dealerFlags)
            ]);
            
            $count++;
        }
        
        $table->render();
        
        if (count($duplicates) > $limit) {
            $remaining = count($duplicates) - $limit;
            $io->note("... and {$remaining} more users with duplicates");
        }
    }

    private function displayDetailedAnalysis(SymfonyStyle $io, array $duplicates, int $limit): void
    {
        $io->section('Detailed Document Analysis');
        
        $count = 0;
        foreach ($duplicates as $userId => $data) {
            if ($count >= $limit) {
                break;
            }
            
            $io->writeln("<info>User ID:</info> {$userId}");
            $io->writeln("<info>Total Documents:</info> {$data['count']}");
            
            $table = new Table($io);
            $table->setHeaders(['Document ID', 'Vehicles', 'PSA IDs', 'Has Dealer', 'Recommendation']);
            
            $scoredDocs = $this->scoreDocuments($data['documents']);
            
            foreach ($scoredDocs as $index => $docData) {
                $doc = $docData['document'];
                $score = $docData['score'];
                $recommendation = $index === 0 ? '🟢 KEEP' : '🔴 DELETE';
                
                $table->addRow([
                    substr($doc['doc_id'], -8),
                    $doc['vehicle_count'],
                    $doc['userPsaId_count'],
                    $doc['has_preferredDealer'] ? 'Yes' : 'No',
                    $recommendation . " (Score: {$score})"
                ]);
            }
            
            $table->render();
            $io->newLine();
            
            $count++;
        }
    }

    private function scoreDocuments(array $documents): array
    {
        $scoredDocs = [];
        
        foreach ($documents as $doc) {
            $score = 0;
            
            // Score based on vehicle count (10 points per vehicle)
            $score += $doc['vehicle_count'] * 10;
            
            // Score based on userPsaId count (5 points per PSA ID)
            $score += $doc['userPsaId_count'] * 5;
            
            // Score based on preferredDealer presence (20 points)
            if ($doc['has_preferredDealer']) {
                $score += 20;
            }
            
            // Score based on document recency (newer ObjectIds get higher score)
            try {
                $objectId = new \MongoDB\BSON\ObjectId($doc['doc_id']);
                $timestamp = $objectId->getTimestamp();
                $score += $timestamp / 1000000; // Small weight for recency
            } catch (\Exception $e) {
                // Ignore timestamp scoring if ObjectId parsing fails
            }
            
            $scoredDocs[] = [
                'document' => $doc,
                'score' => $score
            ];
        }
        
        // Sort by score (highest first)
        usort($scoredDocs, fn($a, $b) => $b['score'] <=> $a['score']);
        
        return $scoredDocs;
    }

    private function exportToJson(array $duplicates, string $filename, SymfonyStyle $io): void
    {
        try {
            $exportData = [
                'generated_at' => date('Y-m-d H:i:s'),
                'total_users_with_duplicates' => count($duplicates),
                'total_duplicate_documents' => array_sum(array_map(fn($item) => $item['count'] - 1, $duplicates)),
                'duplicates' => []
            ];
            
            foreach ($duplicates as $userId => $data) {
                $scoredDocs = $this->scoreDocuments($data['documents']);
                
                $exportData['duplicates'][] = [
                    'user_id' => $userId,
                    'total_documents' => $data['count'],
                    'recommended_to_keep' => $scoredDocs[0]['document']['doc_id'],
                    'documents_to_delete' => array_map(
                        fn($docData) => $docData['document']['doc_id'],
                        array_slice($scoredDocs, 1)
                    ),
                    'documents_details' => array_map(
                        fn($docData) => [
                            'doc_id' => $docData['document']['doc_id'],
                            'score' => $docData['score'],
                            'vehicles' => $docData['document']['vehicle_count'],
                            'psa_ids' => $docData['document']['userPsaId_count'],
                            'has_preferred_dealer' => $docData['document']['has_preferredDealer']
                        ],
                        $scoredDocs
                    )
                ];
            }
            
            file_put_contents($filename, json_encode($exportData, JSON_PRETTY_PRINT));
            $io->success("Results exported to: {$filename}");
            
        } catch (\Exception $e) {
            $io->error("Failed to export results: " . $e->getMessage());
        }
    }

    private function showRecommendations(SymfonyStyle $io, array $duplicates): void
    {
        $io->section('Recommendations');
        
        $totalToDelete = array_sum(array_map(fn($item) => $item['count'] - 1, $duplicates));
        
        $io->listing([
            "Review the detailed analysis to understand which documents should be kept",
            "Documents with more vehicles and PSA IDs are recommended to keep",
            "Documents with preferred dealer data are prioritized",
            "Newer documents (based on ObjectId timestamp) get slight preference",
            "Total documents that can be safely deleted: {$totalToDelete}",
            "Use --export option to save deletion plan for later execution"
        ]);
        
        $io->note('Next steps:');
        $io->writeln('1. Export results: <comment>php bin/console app:find-duplicate-users --export</comment>');
        $io->writeln('2. Review the exported JSON file carefully');
        $io->writeln('3. Create a deletion script based on the recommendations');
        $io->writeln('4. Test deletion script on a backup/staging environment first');
    }
}
