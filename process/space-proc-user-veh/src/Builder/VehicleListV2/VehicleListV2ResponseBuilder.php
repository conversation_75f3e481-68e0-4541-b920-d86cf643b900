<?php

namespace App\Builder\VehicleListV2;

use App\Model\VehicleListV2\VehicleListResponse;
use App\Model\VehicleListV2\VehicleOrder;

class VehicleListV2ResponseBuilder
{
    private string $vin = '';
    private string $id = '';
    private ?string $nickname = '';
    private ?string $label = '';
    private ?string $visual = '';
    private string $sdp = '';
    private bool $isOrder = false;
    private ?VehicleOrder $vehicleOrder = null;

    public function setId(string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function setVin(string $vin): self
    {
        $this->vin = $vin;
        return $this;
    }

    public function setNickname(?string $nickname): self
    {
        $this->nickname = $nickname;
        return $this;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function setVisual(?string $visual): self
    {
        $this->visual = $visual;
        return $this;
    }

    public function setSdp(string $sdp): self
    {
        $this->sdp = $sdp;
        return $this;
    }

    public function setIsOrder(bool $isOrder): self
    {
        $this->isOrder = $isOrder;
        return $this;
    }

    public function setVehicleOrder(?VehicleOrder $vehicleOrder): self
    {
        $this->vehicleOrder = $vehicleOrder;
        return $this;
    }

    public function build(): VehicleListResponse
    {
        return new VehicleListResponse(
            $this->vin,
            $this->id,
            $this->label,
            $this->nickname,
            $this->visual,
            $this->sdp,
            $this->isOrder,
            $this->vehicleOrder
        );
    }
}