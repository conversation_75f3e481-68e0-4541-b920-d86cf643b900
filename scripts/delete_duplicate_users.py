#!/usr/bin/env python3
"""
MongoDB UserData Duplicate Deletion Script

This script executes the deletion of duplicate UserData documents based on
a previously generated deletion plan.

Usage:
    python delete_duplicate_users.py --plan-file deletion_plan.json [--dry-run] [--confirm]

Requirements:
    pip install pymongo python-dotenv tabulate
"""

import os
import sys
import json
import argparse
from datetime import datetime
from typing import Dict, List, Any
from bson import ObjectId

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, OperationFailure
    from dotenv import load_dotenv
    from tabulate import tabulate
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install pymongo python-dotenv tabulate")
    sys.exit(1)


class UserDataDuplicateDeleter:
    def __init__(self, connection_string: str, database_name: str):
        """Initialize the duplicate deleter with MongoDB connection details."""
        self.connection_string = connection_string
        self.database_name = database_name
        self.client = None
        self.db = None
        self.collection = None
        
    def connect(self) -> bool:
        """Establish connection to MongoDB."""
        try:
            print(f"Connecting to MongoDB database: {self.database_name}")
            self.client = MongoClient(self.connection_string)
            
            # Test the connection
            self.client.admin.command('ping')
            
            self.db = self.client[self.database_name]
            self.collection = self.db['userData']
            
            print("✅ Successfully connected to MongoDB")
            return True
            
        except ConnectionFailure as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during connection: {e}")
            return False
    
    def load_deletion_plan(self, plan_file: str) -> Dict[str, Any]:
        """Load deletion plan from JSON file."""
        try:
            with open(plan_file, 'r') as f:
                plan = json.load(f)
            
            print(f"📁 Loaded deletion plan from: {plan_file}")
            print(f"   Users to process: {plan.get('users_to_process', 0)}")
            print(f"   Documents to delete: {plan.get('total_documents_to_delete', 0)}")
            
            return plan
            
        except FileNotFoundError:
            print(f"❌ Deletion plan file not found: {plan_file}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in deletion plan file: {e}")
            return {}
        except Exception as e:
            print(f"❌ Error loading deletion plan: {e}")
            return {}
    
    def validate_deletion_plan(self, plan: Dict[str, Any]) -> bool:
        """Validate that the deletion plan is still accurate."""
        print("\n🔍 Validating deletion plan against current database state...")
        
        if not plan or 'deletion_details' not in plan:
            print("❌ Invalid deletion plan format")
            return False
        
        validation_errors = []
        
        for detail in plan['deletion_details']:
            user_id = detail['user_id']
            expected_total = detail['total_documents']
            keep_doc_id = detail['keep_document']['doc_id']
            delete_doc_ids = [doc['doc_id'] for doc in detail['delete_documents']]
            
            # Check current state in database
            current_docs = list(self.collection.find({"userId": user_id}))
            current_doc_ids = [str(doc['_id']) for doc in current_docs]
            
            # Validate document count
            if len(current_docs) != expected_total:
                validation_errors.append(
                    f"User {user_id}: Expected {expected_total} docs, found {len(current_docs)}"
                )
                continue
            
            # Validate keep document exists
            if keep_doc_id not in current_doc_ids:
                validation_errors.append(
                    f"User {user_id}: Document to keep ({keep_doc_id}) not found"
                )
            
            # Validate delete documents exist
            for delete_doc_id in delete_doc_ids:
                if delete_doc_id not in current_doc_ids:
                    validation_errors.append(
                        f"User {user_id}: Document to delete ({delete_doc_id}) not found"
                    )
        
        if validation_errors:
            print("❌ Validation failed with the following errors:")
            for error in validation_errors[:10]:  # Show first 10 errors
                print(f"   • {error}")
            if len(validation_errors) > 10:
                print(f"   ... and {len(validation_errors) - 10} more errors")
            return False
        
        print("✅ Deletion plan validation passed")
        return True
    
    def preview_deletion(self, plan: Dict[str, Any], limit: int = 5) -> None:
        """Preview what will be deleted."""
        print(f"\n👀 DELETION PREVIEW (showing first {limit} users):")
        print("=" * 60)
        
        for i, detail in enumerate(plan['deletion_details'][:limit]):
            user_id = detail['user_id']
            keep_doc = detail['keep_document']
            delete_docs = detail['delete_documents']
            
            print(f"\n{i+1}. User: {user_id}")
            print(f"   📌 KEEP: {keep_doc['doc_id']}")
            print(f"      Vehicles: {keep_doc['vehicles']}, PSA IDs: {keep_doc['psa_ids']}")
            print(f"      Has Preferred Dealer: {keep_doc['has_preferred_dealer']}")
            
            print(f"   🗑️  DELETE: {len(delete_docs)} documents")
            for j, delete_doc in enumerate(delete_docs):
                print(f"      {j+1}. {delete_doc['doc_id']} "
                      f"(vehicles: {delete_doc['vehicles']}, "
                      f"psa_ids: {delete_doc['psa_ids']})")
        
        if len(plan['deletion_details']) > limit:
            remaining = len(plan['deletion_details']) - limit
            print(f"\n... and {remaining} more users to process")
    
    def execute_deletion(self, plan: Dict[str, Any], dry_run: bool = True) -> Dict[str, Any]:
        """Execute the deletion plan."""
        if dry_run:
            print("\n🧪 DRY RUN MODE - No actual deletions will be performed")
        else:
            print("\n🚨 EXECUTING DELETIONS - This will permanently delete documents!")
        
        results = {
            'users_processed': 0,
            'documents_deleted': 0,
            'errors': [],
            'success_details': []
        }
        
        for detail in plan['deletion_details']:
            user_id = detail['user_id']
            delete_docs = detail['delete_documents']
            
            try:
                deleted_count = 0
                
                for delete_doc in delete_docs:
                    doc_id = delete_doc['doc_id']
                    
                    if dry_run:
                        print(f"   [DRY RUN] Would delete document: {doc_id}")
                        deleted_count += 1
                    else:
                        # Perform actual deletion
                        result = self.collection.delete_one({"_id": ObjectId(doc_id)})
                        
                        if result.deleted_count == 1:
                            print(f"   ✅ Deleted document: {doc_id}")
                            deleted_count += 1
                        else:
                            error_msg = f"Failed to delete document {doc_id} for user {user_id}"
                            print(f"   ❌ {error_msg}")
                            results['errors'].append(error_msg)
                
                results['users_processed'] += 1
                results['documents_deleted'] += deleted_count
                results['success_details'].append({
                    'user_id': user_id,
                    'documents_deleted': deleted_count
                })
                
                print(f"✅ Processed user {user_id}: {deleted_count} documents deleted")
                
            except Exception as e:
                error_msg = f"Error processing user {user_id}: {str(e)}"
                print(f"❌ {error_msg}")
                results['errors'].append(error_msg)
        
        return results
    
    def save_deletion_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """Save deletion results to a JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"deletion_results_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"💾 Deletion results saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving deletion results: {e}")
            return ""
    
    def verify_cleanup(self, plan: Dict[str, Any]) -> None:
        """Verify that duplicates have been cleaned up."""
        print("\n🔍 Verifying cleanup...")
        
        remaining_duplicates = 0
        
        for detail in plan['deletion_details']:
            user_id = detail['user_id']
            current_docs = list(self.collection.find({"userId": user_id}))
            
            if len(current_docs) > 1:
                remaining_duplicates += 1
                print(f"⚠️  User {user_id} still has {len(current_docs)} documents")
            elif len(current_docs) == 1:
                print(f"✅ User {user_id} now has exactly 1 document")
            else:
                print(f"❌ User {user_id} has no documents remaining!")
        
        if remaining_duplicates == 0:
            print("🎉 Cleanup verification passed - no duplicates remaining!")
        else:
            print(f"⚠️  {remaining_duplicates} users still have duplicate documents")
    
    def close(self):
        """Close the MongoDB connection."""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")


def load_environment_config() -> tuple:
    """Load MongoDB configuration from environment variables."""
    # Try to load from .env.local file
    env_file = "process/space-proc-me/.env.local"
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"📁 Loaded environment from: {env_file}")
    else:
        print("⚠️  .env.local file not found, using system environment variables")
    
    connection_string = os.getenv('MONGO_DB_URL')
    database_name = os.getenv('MONGODB_DB')
    
    if not connection_string or not database_name:
        print("❌ Missing required environment variables:")
        print("   MONGO_DB_URL - MongoDB connection string")
        print("   MONGODB_DB - Database name")
        sys.exit(1)
    
    return connection_string, database_name


def main():
    """Main function to run the duplicate deleter."""
    parser = argparse.ArgumentParser(description='Delete duplicate UserData documents')
    parser.add_argument('--plan-file', required=True,
                       help='JSON file containing the deletion plan')
    parser.add_argument('--dry-run', action='store_true',
                       help='Preview deletions without actually deleting')
    parser.add_argument('--confirm', action='store_true',
                       help='Confirm that you want to perform actual deletions')
    parser.add_argument('--save-results', action='store_true',
                       help='Save deletion results to JSON file')
    
    args = parser.parse_args()
    
    print("🗑️  MongoDB UserData Duplicate Deleter")
    print("=" * 50)
    
    # Safety check
    if not args.dry_run and not args.confirm:
        print("❌ For actual deletions, you must use --confirm flag")
        print("   Use --dry-run to preview deletions first")
        sys.exit(1)
    
    # Load configuration
    connection_string, database_name = load_environment_config()
    
    # Initialize deleter
    deleter = UserDataDuplicateDeleter(connection_string, database_name)
    
    try:
        # Connect to MongoDB
        if not deleter.connect():
            sys.exit(1)
        
        # Load deletion plan
        plan = deleter.load_deletion_plan(args.plan_file)
        if not plan:
            sys.exit(1)
        
        # Validate deletion plan
        if not deleter.validate_deletion_plan(plan):
            print("❌ Deletion plan validation failed. Please regenerate the plan.")
            sys.exit(1)
        
        # Preview deletion
        deleter.preview_deletion(plan)
        
        # Confirm before proceeding (if not dry run)
        if not args.dry_run:
            print(f"\n🚨 WARNING: This will permanently delete {plan['total_documents_to_delete']} documents!")
            response = input("Type 'DELETE' to confirm: ")
            if response != 'DELETE':
                print("❌ Deletion cancelled")
                sys.exit(1)
        
        # Execute deletion
        results = deleter.execute_deletion(plan, dry_run=args.dry_run)
        
        # Print results summary
        print(f"\n📊 DELETION RESULTS:")
        print(f"Users processed: {results['users_processed']}")
        print(f"Documents deleted: {results['documents_deleted']}")
        print(f"Errors: {len(results['errors'])}")
        
        if results['errors']:
            print("\n❌ Errors encountered:")
            for error in results['errors']:
                print(f"   • {error}")
        
        # Save results if requested
        if args.save_results:
            deleter.save_deletion_results(results)
        
        # Verify cleanup (only if not dry run)
        if not args.dry_run:
            deleter.verify_cleanup(plan)
    
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        deleter.close()


if __name__ == "__main__":
    main()
