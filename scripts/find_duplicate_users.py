#!/usr/bin/env python3
"""
MongoDB UserData Duplicate Finder Script

This script identifies duplicate UserData documents in the MongoDB database
based on the userId field. It provides detailed analysis and options for
handling duplicates.

Usage:
    python find_duplicate_users.py [--action=analyze|delete] [--dry-run]

Requirements:
    pip install pymongo python-dotenv tabulate
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict
import json
import argparse

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, OperationFailure
    from dotenv import load_dotenv
    from tabulate import tabulate
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install pymongo python-dotenv tabulate")
    sys.exit(1)


class UserDataDuplicateFinder:
    def __init__(self, connection_string: str, database_name: str):
        """Initialize the duplicate finder with MongoDB connection details."""
        self.connection_string = connection_string
        self.database_name = database_name
        self.client = None
        self.db = None
        self.collection = None
        
    def connect(self) -> bool:
        """Establish connection to MongoDB."""
        try:
            print(f"Connecting to MongoDB database: {self.database_name}")
            self.client = MongoClient(self.connection_string)
            
            # Test the connection
            self.client.admin.command('ping')
            
            self.db = self.client[self.database_name]
            self.collection = self.db['userData']
            
            print("✅ Successfully connected to MongoDB")
            return True
            
        except ConnectionFailure as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during connection: {e}")
            return False
    
    def find_duplicates(self) -> Dict[str, List[Dict[str, Any]]]:
        """Find all duplicate UserData documents grouped by userId."""
        print("\n🔍 Searching for duplicate UserData documents...")
        
        try:
            # Aggregation pipeline to find duplicates
            pipeline = [
                {
                    "$group": {
                        "_id": "$userId",
                        "count": {"$sum": 1},
                        "documents": {
                            "$push": {
                                "doc_id": "$_id",
                                "userId": "$userId",
                                "vehicle_count": {"$size": {"$ifNull": ["$vehicle", []]}},
                                "userPsaId_count": {"$size": {"$ifNull": ["$userPsaId", []]}},
                                "has_preferredDealer": {
                                    "$cond": {
                                        "if": {"$ne": ["$preferredDealer", None]},
                                        "then": True,
                                        "else": False
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "$match": {
                        "count": {"$gt": 1}
                    }
                },
                {
                    "$sort": {
                        "count": -1
                    }
                }
            ]
            
            duplicates = list(self.collection.aggregate(pipeline))
            
            if not duplicates:
                print("✅ No duplicate UserData documents found!")
                return {}
            
            print(f"⚠️  Found {len(duplicates)} userIds with duplicate documents")
            
            # Convert to more usable format
            duplicate_dict = {}
            total_duplicate_docs = 0
            
            for item in duplicates:
                user_id = item['_id']
                count = item['count']
                documents = item['documents']
                
                duplicate_dict[user_id] = {
                    'count': count,
                    'documents': documents
                }
                total_duplicate_docs += count - 1  # -1 because we keep one
            
            print(f"📊 Total duplicate documents to be handled: {total_duplicate_docs}")
            
            return duplicate_dict
            
        except Exception as e:
            print(f"❌ Error finding duplicates: {e}")
            return {}
    
    def analyze_duplicates(self, duplicates: Dict[str, List[Dict[str, Any]]]) -> None:
        """Provide detailed analysis of duplicate documents."""
        if not duplicates:
            return
        
        print("\n📈 DUPLICATE ANALYSIS REPORT")
        print("=" * 60)
        
        # Summary statistics
        total_users_with_duplicates = len(duplicates)
        total_duplicate_documents = sum(item['count'] - 1 for item in duplicates.values())
        max_duplicates = max(item['count'] for item in duplicates.values())
        
        print(f"Users with duplicates: {total_users_with_duplicates}")
        print(f"Total duplicate documents: {total_duplicate_documents}")
        print(f"Maximum duplicates for single user: {max_duplicates}")
        
        # Detailed breakdown
        print(f"\n📋 DETAILED BREAKDOWN:")
        
        table_data = []
        for user_id, data in list(duplicates.items())[:20]:  # Show first 20
            count = data['count']
            documents = data['documents']
            
            # Analyze document characteristics
            vehicle_counts = [doc['vehicle_count'] for doc in documents]
            psa_id_counts = [doc['userPsaId_count'] for doc in documents]
            preferred_dealer_flags = [doc['has_preferredDealer'] for doc in documents]
            
            table_data.append([
                user_id[:20] + "..." if len(user_id) > 20 else user_id,
                count,
                f"{min(vehicle_counts)}-{max(vehicle_counts)}",
                f"{min(psa_id_counts)}-{max(psa_id_counts)}",
                sum(preferred_dealer_flags)
            ])
        
        headers = ["User ID", "Duplicates", "Vehicles", "PSA IDs", "Has Dealer"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
        if len(duplicates) > 20:
            print(f"\n... and {len(duplicates) - 20} more users with duplicates")
    
    def get_document_details(self, user_id: str) -> List[Dict[str, Any]]:
        """Get full details of all documents for a specific userId."""
        try:
            documents = list(self.collection.find({"userId": user_id}))
            return documents
        except Exception as e:
            print(f"❌ Error getting document details for {user_id}: {e}")
            return []
    
    def recommend_document_to_keep(self, documents: List[Dict[str, Any]]) -> Optional[str]:
        """
        Recommend which document to keep based on data completeness.
        Priority:
        1. Document with most vehicles
        2. Document with most userPsaId entries
        3. Document with preferredDealer data
        4. Most recent document (if timestamps available)
        """
        if not documents:
            return None
        
        scored_docs = []
        
        for doc in documents:
            score = 0
            doc_id = str(doc['_id'])
            
            # Score based on vehicle count
            vehicles = doc.get('vehicle', [])
            vehicle_count = len(vehicles) if vehicles is not None else 0
            score += vehicle_count * 10

            # Score based on userPsaId count
            psa_ids = doc.get('userPsaId', [])
            psa_id_count = len(psa_ids) if psa_ids is not None else 0
            score += psa_id_count * 5
            
            # Score based on preferredDealer presence
            if doc.get('preferredDealer'):
                score += 20
            
            # Score based on ObjectId timestamp (newer is better)
            try:
                from bson import ObjectId
                if isinstance(doc['_id'], ObjectId):
                    timestamp = doc['_id'].generation_time.timestamp()
                    score += timestamp / 1000000  # Small weight for recency
            except:
                pass
            
            scored_docs.append((doc_id, score, doc))
        
        # Sort by score (highest first)
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        
        return scored_docs[0][0]  # Return document ID with highest score
    
    def generate_deletion_plan(self, duplicates: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Generate a plan for which documents to delete."""
        if not duplicates:
            return {}
        
        deletion_plan = {
            'users_to_process': len(duplicates),
            'total_documents_to_delete': 0,
            'deletion_details': []
        }
        
        for user_id, data in duplicates.items():
            documents = self.get_document_details(user_id)
            
            if len(documents) != data['count']:
                print(f"⚠️  Warning: Document count mismatch for user {user_id}")
                continue
            
            recommended_to_keep = self.recommend_document_to_keep(documents)
            
            docs_to_delete = []
            doc_to_keep = None
            
            for doc in documents:
                doc_id = str(doc['_id'])
                if doc_id == recommended_to_keep:
                    doc_to_keep = {
                        'doc_id': doc_id,
                        'vehicles': len(doc.get('vehicle', [])),
                        'psa_ids': len(doc.get('userPsaId', [])),
                        'has_preferred_dealer': bool(doc.get('preferredDealer'))
                    }
                else:
                    docs_to_delete.append({
                        'doc_id': doc_id,
                        'vehicles': len(doc.get('vehicle', [])),
                        'psa_ids': len(doc.get('userPsaId', [])),
                        'has_preferred_dealer': bool(doc.get('preferredDealer'))
                    })
            
            deletion_plan['deletion_details'].append({
                'user_id': user_id,
                'total_documents': len(documents),
                'keep_document': doc_to_keep,
                'delete_documents': docs_to_delete
            })
            
            deletion_plan['total_documents_to_delete'] += len(docs_to_delete)
        
        return deletion_plan
    
    def save_deletion_plan(self, deletion_plan: Dict[str, Any], filename: str = None) -> str:
        """Save the deletion plan to a JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"userdata_deletion_plan_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(deletion_plan, f, indent=2, default=str)
            
            print(f"💾 Deletion plan saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving deletion plan: {e}")
            return ""
    
    def delete_duplicates(self, user_id: str = None, dry_run: bool = True, force: bool = False, limit: int = None) -> dict:
        """Delete duplicate documents based on scoring algorithm."""
        print(f"🗑️  {'DRY RUN: ' if dry_run else ''}Deleting duplicate UserData documents...")

        if not dry_run and not force:
            confirmation = input("⚠️  This will permanently delete documents! Are you sure? (y/N): ")
            if confirmation.lower() != 'y':
                print("❌ Deletion cancelled by user")
                return {"cancelled": True}

        try:
            # Find duplicates
            duplicates = self.find_duplicates(user_id)
            if not duplicates:
                print("✅ No duplicates found to delete")
                return {"users_processed": 0, "documents_deleted": 0, "errors": []}

            # Limit processing if specified
            if limit and len(duplicates) > limit:
                print(f"📊 Limiting processing to first {limit} users")
                duplicates = dict(list(duplicates.items())[:limit])

            results = {
                "users_processed": 0,
                "documents_deleted": 0,
                "errors": [],
                "details": []
            }

            for user_id_key, data in duplicates.items():
                try:
                    # Score documents to determine which to keep
                    scored_docs = self._score_documents(data['documents'])
                    keep_doc = scored_docs[0]  # Highest scored document
                    docs_to_delete = scored_docs[1:]  # Rest to be deleted

                    deleted_count = 0
                    deleted_ids = []

                    print(f"👤 Processing user: {user_id_key[:20]}...")
                    print(f"   📄 Keeping document: {keep_doc['_id']} (score: {keep_doc.get('score', 0):.2f})")

                    for doc in docs_to_delete:
                        doc_id = doc['_id']

                        if dry_run:
                            print(f"   🔍 [DRY RUN] Would delete: {doc_id}")
                            deleted_count += 1
                            deleted_ids.append(str(doc_id))
                        else:
                            try:
                                from bson import ObjectId
                                delete_result = self.collection.delete_one({"_id": ObjectId(doc_id)})

                                if delete_result.deleted_count == 1:
                                    print(f"   ✅ Deleted: {doc_id}")
                                    deleted_count += 1
                                    deleted_ids.append(str(doc_id))
                                else:
                                    error = f"Failed to delete document {doc_id} for user {user_id_key}"
                                    print(f"   ❌ {error}")
                                    results['errors'].append(error)

                            except Exception as e:
                                error = f"Error deleting document {doc_id}: {str(e)}"
                                print(f"   ❌ {error}")
                                results['errors'].append(error)

                    results['users_processed'] += 1
                    results['documents_deleted'] += deleted_count
                    results['details'].append({
                        'user_id': user_id_key,
                        'kept_document': str(keep_doc['_id']),
                        'deleted_documents': deleted_ids,
                        'deleted_count': deleted_count
                    })

                    print(f"   ✅ Processed: kept 1, deleted {deleted_count}")

                except Exception as e:
                    error = f"Error processing user {user_id_key}: {str(e)}"
                    print(f"❌ {error}")
                    results['errors'].append(error)

            # Summary
            print(f"\n📊 {'DRY RUN ' if dry_run else ''}DELETION SUMMARY:")
            print(f"   Users processed: {results['users_processed']}")
            print(f"   Documents deleted: {results['documents_deleted']}")
            print(f"   Errors: {len(results['errors'])}")

            if results['errors']:
                print("\n❌ ERRORS ENCOUNTERED:")
                for error in results['errors']:
                    print(f"   • {error}")

            return results

        except Exception as e:
            print(f"❌ Unexpected error during deletion: {e}")
            return {"error": str(e), "users_processed": 0, "documents_deleted": 0, "errors": [str(e)]}

    def export_deletion_log(self, results: dict, filename: str = None) -> bool:
        """Export deletion results to JSON file."""
        if filename is None:
            from datetime import datetime
            filename = f"deletion_log_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.json"

        try:
            from datetime import datetime
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "users_processed": results.get('users_processed', 0),
                    "documents_deleted": results.get('documents_deleted', 0),
                    "errors_count": len(results.get('errors', []))
                },
                "details": results.get('details', []),
                "errors": results.get('errors', [])
            }

            with open(filename, 'w') as f:
                import json
                json.dump(log_data, f, indent=2, default=str)

            print(f"📄 Deletion log exported to: {filename}")
            return True

        except Exception as e:
            print(f"❌ Failed to export deletion log: {e}")
            return False

    def close(self):
        """Close the MongoDB connection."""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")


def load_environment_config() -> tuple:
    """Load MongoDB configuration from environment variables."""
    # Try to load from .env.local file
    env_file = "process/space-proc-me/.env.local"
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"📁 Loaded environment from: {env_file}")
    else:
        print("⚠️  .env.local file not found, using system environment variables")
    
    connection_string = os.getenv('MONGO_DB_URL')
    database_name = os.getenv('MONGODB_DB')
    
    if not connection_string or not database_name:
        print("❌ Missing required environment variables:")
        print("   MONGO_DB_URL - MongoDB connection string")
        print("   MONGODB_DB - Database name")
        sys.exit(1)
    
    return connection_string, database_name


def main():
    """Main function to run the duplicate finder."""
    parser = argparse.ArgumentParser(description='Find and analyze duplicate UserData documents')
    parser.add_argument('--action', choices=['analyze', 'plan', 'delete'], default='analyze',
                       help='Action to perform: analyze duplicates, create deletion plan, or delete duplicates')
    parser.add_argument('--save-plan', action='store_true',
                       help='Save deletion plan to JSON file')
    parser.add_argument('--user-id', type=str,
                       help='Analyze duplicates for specific user ID only')
    parser.add_argument('--dry-run', action='store_true',
                       help='Preview deletions without actually deleting')
    parser.add_argument('--force', action='store_true',
                       help='Skip confirmation prompts (use with caution)')
    parser.add_argument('--export-log', type=str,
                       help='Export deletion log to JSON file')
    parser.add_argument('--limit', type=int, default=20,
                       help='Limit number of users to process')
    
    args = parser.parse_args()
    
    print("🔍 MongoDB UserData Duplicate Finder")
    print("=" * 50)
    
    # Load configuration
    connection_string, database_name = load_environment_config()
    
    # Initialize finder
    finder = UserDataDuplicateFinder(connection_string, database_name)
    
    try:
        # Connect to MongoDB
        if not finder.connect():
            sys.exit(1)
        
        # Find duplicates
        duplicates = finder.find_duplicates()
        
        if not duplicates:
            print("✅ No action needed - no duplicates found!")
            return
        
        # Filter by specific user if requested
        if args.user_id:
            if args.user_id in duplicates:
                duplicates = {args.user_id: duplicates[args.user_id]}
                print(f"🎯 Filtering results for user: {args.user_id}")
            else:
                print(f"❌ User {args.user_id} not found in duplicates")
                return
        
        # Perform requested action
        if args.action == 'analyze':
            finder.analyze_duplicates(duplicates)
            
        elif args.action == 'plan':
            print("\n📋 Generating deletion plan...")
            deletion_plan = finder.generate_deletion_plan(duplicates)

            print(f"\n📊 DELETION PLAN SUMMARY:")
            print(f"Users to process: {deletion_plan['users_to_process']}")
            print(f"Documents to delete: {deletion_plan['total_documents_to_delete']}")

            if args.save_plan:
                finder.save_deletion_plan(deletion_plan)

            # Show sample of deletion plan
            print(f"\n📋 SAMPLE DELETION DETAILS (first 5 users):")
            for detail in deletion_plan['deletion_details'][:5]:
                print(f"\nUser: {detail['user_id']}")
                print(f"  Keep: {detail['keep_document']['doc_id']} "
                      f"(vehicles: {detail['keep_document']['vehicles']}, "
                      f"psa_ids: {detail['keep_document']['psa_ids']})")
                print(f"  Delete: {len(detail['delete_documents'])} documents")

        elif args.action == 'delete':
            print("\n🗑️  Performing deletion operation...")

            # Execute deletion
            results = finder.delete_duplicates(
                user_id=args.user_id,
                dry_run=args.dry_run,
                force=args.force,
                limit=args.limit
            )

            # Export log if requested
            if args.export_log and not results.get('cancelled'):
                finder.export_deletion_log(results, args.export_log)
    
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        finder.close()


if __name__ == "__main__":
    main()
