<?php

namespace Tests\Manager;

require __DIR__ . '/../../vendor/autoload.php';

use App\Manager\SubscriptionManager;
use App\Service\SubscriptionService;
use App\Helper\ResponseArrayFormat;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Exception;

class SubscriptionManagerTest extends TestCase
{
    private $subscriptionService;
    private $logger;
    private $subscriptionManager;

    protected function setUp(): void
    {
        $this->subscriptionService = $this->createMock(SubscriptionService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->subscriptionManager = new SubscriptionManager($this->subscriptionService);
        
        $this->subscriptionManager->setLogger($this->logger);
    }

    public function testGetSubscriptionSuccess()
    {
        $userId = 'user123';
        $vin = 'VIN123456';
        $target = 'targetValue';

        // Create mock successful response data
        $mockResponseData = [
            'statusCode' => 200,
            'data' => [
                'subscriptionId' => '12345',
                'status' => 'active'
            ]
        ];

        $mockWSResponse = $this->createMock(WSResponse::class);
        $mockWSResponse->method('getData')
            ->willReturn(json_encode($mockResponseData));

        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userId, $vin, $target)
            ->willReturn($mockWSResponse);

        
        $result = $this->subscriptionManager->getSubscription($userId, $vin, $target);

        // Assertions
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($mockResponseData['data'], $result->getData());
    }

    public function testGetSubscriptionErrorResponse()
    {
        $userId = 'ACNT200000328091';
        $vin = 'VR1URHNSSKW013718';
        $target = 'B2C';
        $errorCode = 404;
        $errorMessage = 'Subscription not found';

        // Create mock error response data
        $mockResponseData = [
            'statusCode' => $errorCode,
            'message' => $errorMessage
        ];

        $mockWSResponse = $this->createMock(WSResponse::class);
        $mockWSResponse->method('getData')
            ->willReturn(json_encode($mockResponseData));

        // Configure subscription service mock
        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userId, $vin, $target)
            ->willReturn($mockWSResponse);

        $result = $this->subscriptionManager->getSubscription($userId, $vin, $target);

        $this->assertInstanceOf(ErrorResponse::class, $result);
    }

    public function testGetSubscriptionException()
    {
        $userId = 'user123';
        $vin = 'VIN123456';
        $target = 'targetValue';
        $exceptionMessage = 'Service unavailable';
        $exceptionCode = 503;

        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userId, $vin, $target)
            ->willThrowException(new Exception($exceptionMessage, $exceptionCode));

        $result = $this->subscriptionManager->getSubscription($userId, $vin, $target);

        $this->assertInstanceOf(ErrorResponse::class, $result);
    }

    public function testGetSubscriptionInvalidJson()
    {
        $userId = 'user123';
        $vin = 'VIN123456';
        $target = 'targetValue';

        $mockWSResponse = new WSResponse(500, json_encode(['statusCode'=>500,'message'=>'failure']));

        $this->subscriptionService->expects($this->once())
            ->method('getSubscription')
            ->with($userId, $vin, $target)
            ->willReturn($mockWSResponse);

        $result = $this->subscriptionManager->getSubscription($userId, $vin, $target);

        $this->assertInstanceOf(ErrorResponse::class, $result);
    }
}