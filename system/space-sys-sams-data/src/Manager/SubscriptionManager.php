<?php

namespace App\Manager;

use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

use App\Helper\BundlesTransformer;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use App\Service\SubscriptionService;

class SubscriptionManager
{
    use LoggerTrait;
    
    /**
     * @var SubscriptionService
     */
    private $service;

    public function __construct(SubscriptionService $service)
    {
        $this->service = $service;
    }

    /**
     * get Catalog data with contrib from SAMS V2
     * @param string $userId
     * @param string $vin
     * @param string $target
     * @return ResponseArrayFormat
     */
    public function getSubscription(string $userId, string $vin, string $target): ResponseArrayFormat {
        try {
            $subscription = $this->service->getSubscription($userId, $vin, $target);
            $subscription = $subscription->getData();
            $subscription = json_decode($subscription, true);
            if (isset($subscription["statusCode"]) && $subscription["statusCode"] !== 200) {
                $this->logger->error("Error: SAMS contract response for vin {$vin} ({$subscription["statusCode"]})");
                $statusCode = $subscription["statusCode"];
                $responseData = $subscription["message"];
                if (is_object($responseData) && $responseData instanceof \stdClass && isset($responseData->message))
                {
                    $message = isset($responseData->message) ? $responseData->message : $responseData;
                    $this->logger->error('=> '.__METHOD__.' => error : '.$message . 'for vin : {$vin}');
                    return new ErrorResponse($message, $statusCode);
                }
                $message = (isset(json_decode($responseData->error)->message)) ? json_decode($responseData->error)->message : $responseData;
                $this->logger->error('=> '.__METHOD__.' => error : '.$message . 'for vin : {$vin}');
                return new ErrorResponse($message, $statusCode);
            }
            else {
                $this->logger->info("Success: [function getCatalog] Call SAMS catalog for vin {$vin}");
                return new SuccessResponse($subscription["data"]);
            }
        }
        catch (Exception $e) {
            $this->logger->error("Error: [function getCatalog] Call SAMS catalog for vin {$vin} ({$e->getMessage()})");
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}