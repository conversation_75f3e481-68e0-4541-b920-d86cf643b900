<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Model;

use App\Trait\ValidationResponseTrait;
use App\Helper\ErrorResponse;
use App\Validator\VinValidator;
use App\Manager\SubscriptionManager;

#[Route('/v1/subscription', name: 'Subscription_')]
class SubscriptionController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'getSubscription', methods: ['GET'])]
    #[OA\Tag(name: 'SUBSCRIPTION API')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'query',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type: 'object')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getSubscription(Request $request, SubscriptionManager $subscriptionManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $target = $request->query->get('target') ?? '<string>';
        $errors = $validator->validate(
            compact('vin', 'userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        $response = $subscriptionManager->getSubscription($userId, $vin, $target)->toArray();
        return $this->json($response['content'], $response['code']);
    }
}