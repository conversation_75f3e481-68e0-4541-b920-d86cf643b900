<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * Kind of HttpClientInterface decorator.
 */
class CustomHttpClient
{
    use LoggerTrait;

    private InternalHttpClientService $client;

    public function __construct(InternalHttpClientService $client)
    {
        $this->client = $client;
    }

    public function request(string $method, string $url, array $options = []): WSResponse
    {
        try {
            // Log request details
            $this->logger->info("REQUEST: {$method} {$url}", [
                'method' => $method,
                'url' => $url,
                'options' => json_encode($options)
            ]);
            
            $response = $this->client->request($method, $url, $options);
            $this->logger->debug('Full response logs: {logs}', ['logs' => $response->getInfo('debug')]);
            $statusCode = $response->getStatusCode();
            $data = Response::HTTP_NO_CONTENT != $statusCode ? $response->toArray(false) : [];
            
            // Log response details
            $this->logger->info("RESPONSE: {$method} {$url}", [
                'status_code' => $statusCode,
                'data' => json_encode($data)
            ]);
            
            $wsResponse = new WSResponse($statusCode, $data);

            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Cached Exception : CustomHttpClient::request '.$e->getMessage(), [
                'method' => $method,
                'url' => $url,
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}